import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Node;

import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Iterator;
import java.util.List;
import java.util.Scanner;

public class JenkinsJobCreate {
    private static final String SOURCE_VIEW = "https://jenkins.gw-ams.com/view/夏初测试/";
    private static final String TARGET_VIEW = "https://jenkins.gw-ams.com/view/夏初/";
    private static final String USERNAME = "gongyuanhui";
    private static final String PASSWORD = "9u^Les2tcTLLtmct";
    private static final String SUFFIX = "v2";

    public static void main(String[] args) {
        try {
            // 获取源视图中的所有任务
            List<String> sourceJobs = getJobsFromView(SOURCE_VIEW);
            List<String> target = getJobsFromView(TARGET_VIEW);
            System.out.println("Found " + sourceJobs.size() + " jobs in source view");
            ChangeConfig changeConfig = new ChangeConfig("*/xiachu");
            // 复制每个任务到目标视图并添加后缀
            for (String jobName : sourceJobs) {
                String newJobName = jobName.replaceAll("xcts", "xc");
                if (target.contains(newJobName)) {
                    continue;
                }
                String config = changeConfig(getJobConfig(jobName), changeConfig);
                System.out.println(newJobName);
                createJob(newJobName, jobConfig);
            }

            System.out.println("All jobs copied successfully!");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String changeConfig(String jobConfig, ChangeConfig branchName) throws DocumentException {
        Document document = DocumentHelper.parseText(jobConfig);
        changeBranch(document, branchName);
        return document.asXML();
    }

    private static void changeBranch(Document doc, ChangeConfig changeConfig) {
        String branchName = changeConfig.branchName();
        if (branchName.isBlank()) {
            return;
        }
        List<?> list = doc.selectNodes("/project/scm/scms/hudson.plugins.git.GitSCM");
        for (Object next : list) {
            if (next instanceof Element e) {
                Element element = e.element("userRemoteConfigs").element("hudson.plugins.git.UserRemoteConfig").element("url");
                String url = element.getText();
                if (url.equals("https://git.gw-ams.com/ziguan/cloud-config-center.git")) {
                    continue;
                }
                Element branchEle = e.element("branches").element("hudson.plugins.git.BranchSpec").element("name");
                branchEle.setText(branchName);
            }
        }
    }

    private static void print(Element e) {
        String path = e.getPath();
        System.out.println(path);
        List<?> elements = e.elements();
        if (elements.isEmpty()) {
            System.out.println(e.getText());
        } else {
            for (Object element : elements) {
                if (element instanceof Element child) {
                    print(child);
                }
            }
        }
    }

    private static List<String> getJobsFromView(String viewUrl) throws Exception {
        List<String> jobs = new ArrayList<>();
        // 对URL进行编码处理
        String encodedViewUrl = encodeUrl(viewUrl);
        String apiUrl = encodedViewUrl + "api/json?tree=jobs[name]";
        String response = sendRequest(apiUrl, "GET", null);

        // 简单解析JSON响应获取任务名称
        // 注意：实际项目中应使用JSON库如Jackson或Gson
        String[] jobEntries = response.split("\"name\":");
        for (int i = 1; i < jobEntries.length; i++) {
            String name = jobEntries[i].split("\"")[1];
            jobs.add(name);
        }

        return jobs;
    }

    private static String getJobConfig(String jobName) throws Exception {
        // 对URL和任务名进行编码处理
        String encodedViewUrl = encodeUrl(SOURCE_VIEW);
        String encodedJobName = URLEncoder.encode(jobName, StandardCharsets.UTF_8);
        String configUrl = encodedViewUrl + "job/" + encodedJobName + "/config.xml";
        return sendRequest(configUrl, "GET", null);
    }

    private static void createJob(String newJobName, String configXml) throws Exception {
        // 对URL和任务名进行编码处理
        String encodedViewUrl = encodeUrl(TARGET_VIEW);
        String encodedJobName = URLEncoder.encode(newJobName, StandardCharsets.UTF_8);
        String createUrl = encodedViewUrl + "createItem?name=" + encodedJobName;
        sendRequest(createUrl, "POST", configXml);
    }

    private static String encodeUrl(String url) throws Exception {
        // 将URL分解并编码路径部分
        URL originalUrl = new URL(url);
        String path = originalUrl.getPath();
        String[] pathSegments = path.split("/");
        StringBuilder encodedPath = new StringBuilder();

        for (String segment : pathSegments) {
            if (!segment.isEmpty()) {
                encodedPath.append("/").append(URLEncoder.encode(segment, StandardCharsets.UTF_8));
            }
        }
        if (path.endsWith("/")) {
            encodedPath.append("/");
        }

        return new URL(originalUrl.getProtocol(), originalUrl.getHost(), originalUrl.getPort(),
                encodedPath.toString()).toString();
    }

    private static String sendRequest(String urlStr, String method, String body) throws Exception {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod(method);

        // 设置基本认证
        String auth = USERNAME + ":" + PASSWORD;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
        conn.setRequestProperty("Authorization", "Basic " + encodedAuth);

        if (body != null) {
            conn.setDoOutput(true);
            conn.setRequestProperty("Content-Type", "application/xml");
            conn.getOutputStream().write(body.getBytes());
        }

        // 读取响应
        StringBuilder response = new StringBuilder();
        try (Scanner scanner = new Scanner(conn.getInputStream())) {
            while (scanner.hasNextLine()) {
                response.append(scanner.nextLine());
            }
        }

        return response.toString();
    }
}